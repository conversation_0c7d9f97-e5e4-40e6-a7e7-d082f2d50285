{"name": "My workflow 2", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-5120, 3296], "id": "1d0e38c9-f314-4d92-933e-7e90212b465c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [-912, 2016], "typeVersion": 1, "id": "5eef875c-3252-4a82-b612-a08795e37034", "name": "Sticky Note1"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [-1760, 1424], "typeVersion": 1, "id": "e49cf8c0-1d99-4f80-a94e-b4ae826db78f", "name": "Sticky Note2"}, {"parameters": {"jsCode": "// PREPARE LEADS NODE - Clean business name and phone\nconst items = $input.all();\n\nconst preparedLeads = items.map((item, index) => {\n  const data = item.json;\n  \n  // Clean business name - remove Lithuanian characters and special chars\n  let cleanBusinessName = data.business_name || data.title || data.name || '';\n  cleanBusinessName = cleanBusinessName\n    .replace(/[ąčęėįšųūž]/gi, match => {\n      const replacements = {\n        'ą': 'a', 'Ą': 'A', 'č': 'c', 'Č': 'C', 'ę': 'e', 'Ę': 'E',\n        'ė': 'e', 'Ė': 'E', 'į': 'i', 'Į': 'I', 'š': 's', 'Š': 'S',\n        'ų': 'u', 'Ų': 'U', 'ū': 'u', 'Ū': 'U', 'ž': 'z', 'Ž': 'Z'\n      };\n      return replacements[match] || match;\n    })\n    .replace(/[,|]/g, '') // Remove commas and pipes\n    .trim();\n\n  // Clean phone number - remove all non-digits and add +370 if needed\n  let cleanPhone = '';\n  const phoneFields = [data.google_phone, data.phone, data.phoneNumber, data.owner_phone];\n  \n  for (const phoneField of phoneFields) {\n    if (phoneField && phoneField.trim()) {\n      cleanPhone = phoneField.replace(/\\D/g, '');\n      if (cleanPhone.startsWith('370')) {\n        cleanPhone = '+' + cleanPhone;\n      } else if (cleanPhone.startsWith('8') && cleanPhone.length === 9) {\n        cleanPhone = '+370' + cleanPhone.substring(1);\n      } else if (!cleanPhone.startsWith('+370') && cleanPhone.length === 8) {\n        cleanPhone = '+370' + cleanPhone;\n      }\n      if (cleanPhone.length >= 12) break; // Valid phone found\n    }\n  }\n\n  return {\n    ...data,\n    clean_business_name: cleanBusinessName,\n    clean_phone: cleanPhone,\n    row_index: index + 2 // Google Sheets is 1-indexed, +1 for header\n  };\n});\n\n// Filter out items without valid phone numbers\nconst validLeads = preparedLeads.filter(lead => lead.clean_phone && lead.clean_phone.length >= 12);\n\nreturn validLeads.map(lead => ({ json: lead }));"}, "id": "52cae7c5-02fc-48be-b21d-0b665ea9ebc6", "name": "Prepare Leads1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3632, 3376]}, {"parameters": {"operation": "getAll", "tableId": "sms_tracking", "limit": 1000}, "id": "c4d51d1b-29fc-4aeb-8f85-f1ef4b780349", "name": "Check Database for Existing Phones1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-3424, 3376], "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// DATABASE CHECK AND SHEET MARKING\nconst leads = $('Prepare Leads1').all();\nconst existingPhones = $('Check Database for Existing Phones1').all();\n\n// Create set of existing phone numbers for fast lookup\nconst phoneSet = new Set(existingPhones.map(item => item.json.phone_number));\n\nconst processedLeads = leads.map(lead => {\n  const data = lead.json;\n  const phoneExists = phoneSet.has(data.clean_phone);\n  \n  return {\n    ...data,\n    sms_sent: phoneExists ? 'yes' : 'no',\n    sms_status: phoneExists ? 'already_sent' : 'unsent',\n    phone_exists_in_db: phoneExists\n  };\n});\n\nreturn processedLeads.map(lead => ({ json: lead }));"}, "id": "c3cb386f-6bb6-442a-9f8d-4ff0a4eaf25f", "name": "Mark SMS Status in Data1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3232, 3376]}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "business_name": "={{ $json.business_name }}"}, "matchingColumns": ["address"], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "USER_ENTERED"}}, "id": "b0801154-2e67-41c7-b7ea-c371f0cb024a", "name": "Update Google Sheet Status1", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3024, 3376], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// FILTER FOR SMS - Only leads where sms_sent is 'no'\nconst leads = $input.all();\n\nconst unsentLeads = leads.filter(lead => {\n  return lead.json.sms_sent === 'no';\n});\n\nconsole.log(`Filtered ${unsentLeads.length} leads for SMS sending (out of ${leads.length} total)`);\nconsole.log('Leads with sms_sent=\"no\":', unsentLeads.map(lead => ({\n  business_name: lead.json.business_name,\n  sms_sent: lead.json.sms_sent,\n  phone: lead.json.clean_phone\n})));\n\nreturn unsentLeads;"}, "id": "bc21d855-28d5-41d4-9f5f-261caeaa31ca", "name": "Filter Leads for SMS1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2832, 3392]}, {"parameters": {"jsCode": "// PROCESS SMS RESPONSE - Update status based on SMS results\nconst smsResults = $input.all();\n\nconsole.log('=== PROCESSING SMS RESPONSES ===');\nconsole.log(`Total SMS responses: ${smsResults.length}`);\n\nconst processedResults = smsResults.map((result, index) => {\n  const originalData = result.json;\n  const smsResponse = originalData.sms_response || '';\n  \n  console.log(`SMS ${index + 1} Response:`, {\n    business_name: originalData.business_name,\n    phone: originalData.clean_phone,\n    raw_response: smsResponse\n  });\n  \n  // Check SMS8 response format - look for success indicators in text response\n  const smsSuccess = smsResponse.includes('success') || smsResponse.includes('sent') || smsResponse.includes('delivered') || smsResponse.includes('OK');\n  \n  console.log(`SMS ${index + 1} Success:`, smsSuccess);\n  \n  return {\n    // Keep ALL original data from Set node\n    ...originalData,\n    // Update SMS status based on response\n    sms_status: smsSuccess ? 'success' : 'failed',\n    sms_error: smsSuccess ? null : smsResponse,\n    // Ensure key fields are preserved\n    address: originalData.address,\n    business_name: originalData.business_name,\n    clean_phone: originalData.clean_phone,\n    sms_sent: smsSuccess ? 'yes' : 'no'\n  };\n});\n\nconsole.log('=== FINAL PROCESSING RESULTS ===');\nprocessedResults.forEach((result, index) => {\n  console.log(`Result ${index + 1}:`, {\n    business_name: result.business_name,\n    sms_status: result.sms_status,\n    sms_sent: result.sms_sent\n  });\n});\n\nreturn processedResults.map(result => ({ json: result }));"}, "id": "03c047b5-4d14-4b38-a6d1-3749087bf5bd", "name": "Process SMS Response1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1968, 3376]}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"sms_status": "={{ $json.sms_status }}", "address": "={{ $json.address }}"}, "matchingColumns": ["address"], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "0512eb74-716c-4dd6-84d0-169ffefe6df7", "name": "Update Final SMS Status1", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1616, 3280], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// FILTER SUCCESSFUL SMS - Only record successful SMS to database\nconst smsResults = $input.all();\n\nconsole.log('=== SMS FILTER DEBUG ===');\nconsole.log(`Total SMS results received: ${smsResults.length}`);\n\n// Debug each SMS result\nsmsResults.forEach((result, index) => {\n  console.log(`SMS ${index + 1}:`, {\n    business_name: result.json.business_name,\n    sms_status: result.json.sms_status,\n    sms_response: result.json.sms_response,\n    clean_phone: result.json.clean_phone\n  });\n});\n\nconst successfulSMS = smsResults.filter(result => {\n  const status = result.json.sms_status;\n  return status === 'success';\n});\n\nconsole.log(`Successful SMS found: ${successfulSMS.length}`);\nconsole.log(`Failed SMS: ${smsResults.length - successfulSMS.length}`);\n\n// If no successful SMS, still return empty array (don't stop workflow)\nif (successfulSMS.length === 0) {\n  console.log('⚠️ No successful SMS to record in database');\n  return [];\n}\n\nconsole.log('✅ Recording successful SMS to database:', successfulSMS.map(s => s.json.business_name));\nreturn successfulSMS;"}, "id": "f1e2d3c4-a5b6-7890-cdef-123456789abc", "name": "Filter Successful SMS for DB", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1760, 3488]}, {"parameters": {"operation": "create", "tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "phone_number", "fieldValue": "={{ $json.clean_phone }}"}, {"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}]}}, "id": "1db3d068-c1c5-402f-8a22-8debd7c7dde8", "name": "Record Successful SMS to Database1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1616, 3488], "executeOnce": false, "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Realistic dummy data for testing the workflow\n// This simulates the output from Apify Google Maps scraper\nconst mockBusinessData = [\n  {\n    \"title\": \"Gėlių Namai\",\n    \"address\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/geliu-namai-vilnius\",\n    \"categoryName\": \"Gėlių parduotuvė\",\n    \"category\": \"Gėlių parduotuvė\",\n    \"totalScore\": 4.3,\n    \"rating\": 4.3,\n    \"reviewsCount\": 28,\n    \"location\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"name\": \"Gėlių Namai\"\n  },\n  {\n    \"title\": \"<PERSON><PERSON><PERSON>\",\n    \"address\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/kavine-sauletekis-kauna<PERSON>\",\n    \"categoryName\": \"Kavinė\",\n    \"category\": \"Kavinė\",\n    \"totalScore\": 4.7,\n    \"rating\": 4.7,\n    \"reviewsCount\": 45,\n    \"location\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"name\": \"Kavinė Saulėtekis\"\n  },\n  {\n    \"title\": \"Grožio Salonas Žvaigždė\",\n    \"address\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"phone\": \"+37060333333\",\n    \"phoneNumber\": \"+37060333333\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/grozio-salonas-zvaigzde\",\n    \"categoryName\": \"Grožio salonas\",\n    \"category\": \"Grožio salonas\",\n    \"totalScore\": 4.1,\n    \"rating\": 4.1,\n    \"reviewsCount\": 19,\n    \"location\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"name\": \"Grožio Salonas Žvaigždė\"\n  },\n  {\n    \"title\": \"Restoranas Šiaurės Žvaigždė\",\n    \"address\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"phone\": \"+37060444444\",\n    \"phoneNumber\": \"+37060444444\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/restoranas-siaures-zvaigzde\",\n    \"categoryName\": \"Restoranas\",\n    \"category\": \"Restoranas\",\n    \"totalScore\": 4.5,\n    \"rating\": 4.5,\n    \"reviewsCount\": 67,\n    \"location\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"name\": \"Restoranas Šiaurės Žvaigždė\"\n  },\n  {\n    \"title\": \"Autoservisas Greitas Ratas\",\n    \"address\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/autoservisas-greitas-ratas\",\n    \"categoryName\": \"Autoservisas\",\n    \"category\": \"Autoservisas\",\n    \"totalScore\": 3.9,\n    \"rating\": 3.9,\n    \"reviewsCount\": 34,\n    \"location\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"name\": \"Autoservisas Greitas Ratas\"\n  }\n];\n\nconsole.log('Mock Business Data - Generated test data:', mockBusinessData.length, 'businesses');\nreturn mockBusinessData.map(data => ({ json: data }));"}, "id": "2e79ed48-a787-40d2-98b0-1fb21f9a411a", "name": "Test Business Data1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4912, 3280]}, {"parameters": {"jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\nconst seenBusinesses = new Set(); // Track duplicates within this batch\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const businessName = data.title || data.name || 'Unknown';\n    const address = data.address || data.location || 'Unknown';\n    \n    // Create a unique key for deduplication (business name + address)\n    const uniqueKey = `${businessName.toLowerCase().trim()}_${address.toLowerCase().trim()}`;\n    \n    // Skip if we've already seen this business in this batch\n    if (seenBusinesses.has(uniqueKey)) {\n      console.log(`Skipping duplicate business: ${businessName}`);\n      continue;\n    }\n    \n    seenBusinesses.add(uniqueKey);\n    \n    const lead = {\n      business_name: businessName,\n      address: address,\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || '',\n      director_name: '', // Will be filled from rekvizitai\n      owner_email: '' // Will be filled from rekvizitai\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} unique businesses without websites (removed ${businesses.length - filteredLeads.length} duplicates)`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}, "id": "e3ca10b8-2882-41d0-8927-33ee56ea2242", "name": "Filter Businesses Without Website1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4640, 3264]}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.address }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-4048, 3312], "id": "59152287-9b26-485b-ac7b-8620759b4666", "name": "Remove Duplicates1"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw", "mode": "list", "cachedResultName": "Leads_Unfiltered", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone.replace('+', '') }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_status }}", "sms_status": "={{ $json.sms_status }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "ddb30e67-3e67-4ec8-9d16-92bb0e161525", "name": "Unfiltered_Leads1", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-4208, 3296], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}", "sms_status": "={{ $json.sms_status }}", "sms_sent": "={{ $json.sms_sent }}", "phone_source": "={{ $json.phone_source }}", "owner_phone": "={{ $json.owner_phone }}", "website": "={{ $json.website }}", "google_phone": "={{ $json.google_phone}}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "00faf018-9cd2-4000-abb4-f5b6235c7590", "name": "Filtered_Leads1", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3840, 3392], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  // Normalize reviews_count\n  let count = item.json.reviews_count;\n  if (count === undefined || count === null || count === '') {\n    item.json.reviews_count = 0;\n  } else {\n    item.json.reviews_count = Number(count);\n  }\n\n  // Normalize rating\n  let rating = item.json.rating;\n  if (rating === undefined || rating === null || rating === '') {\n    item.json.rating = 0;\n  } else {\n    item.json.rating = Number(rating);\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4400, 3296], "id": "3e5412cc-52aa-4b7e-89d4-d2165217e978", "name": "Normalise reviews, and1"}, {"parameters": {"url": "https://app.sms8.io/services/send.php", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "dadea4239d37bc693307d751a7be0be596d80bed"}, {"name": "number", "value": "={{ ($json.google_phone || $json.owner_phone).replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"name": "message", "value": "=<PERSON><PERSON><PERSON>, <PERSON><PERSON> iš Upzera👋!\n\n<PERSON><PERSON> jauna komanda, pad<PERSON><PERSON> verslams sukurti svetaines ir pritraukti daugiau klientų pagerinant matomumą internetinėje erdvėje.\n\nNorėčiau trumpai pasidalinti idėja, kaip ne<PERSON>ti potencialių klientų – ar domintų sužinoti daugiau?\n\nupzera.com/lt"}, {"name": "device", "value": "[\"3771 | 0\"]"}, {"name": "type", "value": "sms"}, {"name": "prioritize", "value": "0"}]}, "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {"response": {"response": {"responseFormat": "text", "outputPropertyName": "sms_response"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2592, 3520], "id": "20ccd9ac-902c-4d7f-8fd1-a08a5ea1f542", "name": "SMS1"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-2352, 3344], "id": "097acf9f-8002-45cf-ab8a-4028fe09b88a", "name": "<PERSON><PERSON>"}], "pinData": {"When clicking ‘Execute workflow’": [{"json": {"query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Lietuva"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Test Business Data1", "type": "main", "index": 0}]]}, "Prepare Leads1": {"main": [[{"node": "Check Database for Existing Phones1", "type": "main", "index": 0}]]}, "Check Database for Existing Phones1": {"main": [[{"node": "Mark SMS Status in Data1", "type": "main", "index": 0}]]}, "Mark SMS Status in Data1": {"main": [[{"node": "Update Google Sheet Status1", "type": "main", "index": 0}]]}, "Update Google Sheet Status1": {"main": [[{"node": "Filter Leads for SMS1", "type": "main", "index": 0}]]}, "Filter Leads for SMS1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}, {"node": "SMS1", "type": "main", "index": 0}]]}, "Process SMS Response1": {"main": [[{"node": "Update Final SMS Status1", "type": "main", "index": 0}, {"node": "Filter Successful SMS for DB", "type": "main", "index": 0}]]}, "Filter Successful SMS for DB": {"main": [[{"node": "Record Successful SMS to Database1", "type": "main", "index": 0}]]}, "Test Business Data1": {"main": [[{"node": "Filter Businesses Without Website1", "type": "main", "index": 0}]]}, "Filter Businesses Without Website1": {"main": [[{"node": "Normalise reviews, and1", "type": "main", "index": 0}]]}, "Remove Duplicates1": {"main": [[{"node": "Filtered_Leads1", "type": "main", "index": 0}]]}, "Unfiltered_Leads1": {"main": [[{"node": "Remove Duplicates1", "type": "main", "index": 0}]]}, "Filtered_Leads1": {"main": [[{"node": "Prepare Leads1", "type": "main", "index": 0}]]}, "Normalise reviews, and1": {"main": [[{"node": "Unfiltered_Leads1", "type": "main", "index": 0}]]}, "SMS1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Process SMS Response1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0ba56d7d-68f7-48e0-b2b5-3e4179db2d34", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e792ababaacd3609090c67925029f9ce56f8b2294355abafc6b7c9d2271c36bb"}, "id": "5JmQD48moWbfAuMi", "tags": []}