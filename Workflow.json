{"name": "My workflow 2", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-4704, 2496], "id": "1d0e38c9-f314-4d92-933e-7e90212b465c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [-912, 2016], "typeVersion": 1, "id": "5eef875c-3252-4a82-b612-a08795e37034", "name": "Sticky Note1"}, {"parameters": {"content": "Problem here"}, "type": "n8n-nodes-base.stickyNote", "position": [-1760, 1424], "typeVersion": 1, "id": "e49cf8c0-1d99-4f80-a94e-b4ae826db78f", "name": "Sticky Note2"}, {"parameters": {"jsCode": "// PREPARE LEADS NODE - Clean business name and phone\nconst items = $input.all();\n\nconst preparedLeads = items.map((item, index) => {\n  const data = item.json;\n  \n  // Clean business name - remove Lithuanian characters and special chars\n  let cleanBusinessName = data.business_name || data.title || data.name || '';\n  cleanBusinessName = cleanBusinessName\n    .replace(/[ąčęėįšųūž]/gi, match => {\n      const replacements = {\n        'ą': 'a', 'Ą': 'A', 'č': 'c', 'Č': 'C', 'ę': 'e', 'Ę': 'E',\n        'ė': 'e', 'Ė': 'E', 'į': 'i', 'Į': 'I', 'š': 's', 'Š': 'S',\n        'ų': 'u', 'Ų': 'U', 'ū': 'u', 'Ū': 'U', 'ž': 'z', 'Ž': 'Z'\n      };\n      return replacements[match] || match;\n    })\n    .replace(/[,|]/g, '') // Remove commas and pipes\n    .trim();\n\n  // Clean phone number - remove all non-digits and add +370 if needed\n  let cleanPhone = '';\n  const phoneFields = [data.google_phone, data.phone, data.phoneNumber, data.owner_phone];\n  \n  for (const phoneField of phoneFields) {\n    if (phoneField && phoneField.trim()) {\n      cleanPhone = phoneField.replace(/\\D/g, '');\n      if (cleanPhone.startsWith('370')) {\n        cleanPhone = '+' + cleanPhone;\n      } else if (cleanPhone.startsWith('8') && cleanPhone.length === 9) {\n        cleanPhone = '+370' + cleanPhone.substring(1);\n      } else if (!cleanPhone.startsWith('+370') && cleanPhone.length === 8) {\n        cleanPhone = '+370' + cleanPhone;\n      }\n      if (cleanPhone.length >= 12) break; // Valid phone found\n    }\n  }\n\n  return {\n    ...data,\n    clean_business_name: cleanBusinessName,\n    clean_phone: cleanPhone,\n    row_index: index + 2 // Google Sheets is 1-indexed, +1 for header\n  };\n});\n\n// Filter out items without valid phone numbers\nconst validLeads = preparedLeads.filter(lead => lead.clean_phone && lead.clean_phone.length >= 12);\n\nreturn validLeads.map(lead => ({ json: lead }));"}, "id": "********-5c13-4b37-b496-251acc06305e", "name": "Prepare Leads", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3168, 2624]}, {"parameters": {"operation": "getAll", "tableId": "sms_tracking", "limit": 1000}, "id": "049fa492-a917-41cb-8068-1318b057bfb5", "name": "Check Database for Existing Phones", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2960, 2624], "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// DATABASE CHECK AND SHEET MARKING\nconst leads = $('Prepare Leads').all();\nconst existingPhones = $('Check Database for Existing Phones').all();\n\n// Create set of existing phone numbers for fast lookup\nconst phoneSet = new Set(existingPhones.map(item => item.json.phone_number));\n\nconst processedLeads = leads.map(lead => {\n  const data = lead.json;\n  const phoneExists = phoneSet.has(data.clean_phone);\n  \n  return {\n    ...data,\n    sms_sent: phoneExists ? 'yes' : 'no',\n    sms_status: phoneExists ? 'already_sent' : 'unsent',\n    phone_exists_in_db: phoneExists\n  };\n});\n\nreturn processedLeads.map(lead => ({ json: lead }));"}, "id": "c30a6a9d-bb45-4ff0-af2c-f7be9b31c60a", "name": "<PERSON> SMS Status in Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2768, 2624]}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "address": "={{ $json.address }}"}, "matchingColumns": ["address"], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "USER_ENTERED"}}, "id": "e7a8920d-04d4-400f-bac9-3610745f6c4e", "name": "Update Google Sheet Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2560, 2624], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// FILTER FOR SMS - Only leads not found in database\nconst leads = $input.all();\n\nconst unsentLeads = leads.filter(lead => {\n  return lead.json.phone_exists_in_db === false;\n});\n\nconsole.log(`Filtered ${unsentLeads.length} leads for SMS sending (out of ${leads.length} total)`);\n\nreturn unsentLeads;"}, "id": "7922ea4d-5fd6-43ee-bc28-f4ab12bbcbb0", "name": "Filter Leads for SMS", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2368, 2624]}, {"parameters": {"jsCode": "// PROCESS SMS RESPONSE - Update status based on SMS results\nconst smsResults = $input.all();\n\nconst processedResults = smsResults.map(result => {\n  const originalData = result.json;\n  const smsResponse = originalData.response || {};\n  \n  // Check SMS8 response format - adjust based on actual API response\n  const smsSuccess = smsResponse.status === 'success' || smsResponse.code === 200 || smsResponse.success === true;\n  \n  return {\n    ...originalData,\n    sms_status: smsSuccess ? 'success' : 'failed',\n    sms_error: smsSuccess ? null : (smsResponse.message || smsResponse.error || 'Unknown error')\n  };\n});\n\nreturn processedResults.map(result => ({ json: result }));"}, "id": "ddc96513-102b-41a4-944d-777319818dd7", "name": "Process SMS Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1968, 2624]}, {"parameters": {"operation": "update", "documentId": "YOUR_GOOGLE_SHEET_ID", "sheetName": "Sheet1", "columns": {"mappingMode": "defineBelow", "value": {}}, "options": {}}, "id": "657b281a-37a1-4481-bbe8-30eb443b26d3", "name": "Update Final SMS Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1728, 2560], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"tableId": "sms_tracking"}, "id": "65db4e09-3fb5-4b68-b1f5-e23b3db120c0", "name": "Record Successful SMS to Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1712, 2832], "executeOnce": false, "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"jsCode": "// Realistic dummy data for testing the workflow\n// This simulates the output from Apify Google Maps scraper\nconst mockBusinessData = [\n  {\n    \"title\": \"Gėlių Namai\",\n    \"address\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/geliu-namai-vilnius\",\n    \"categoryName\": \"Gėlių parduotuvė\",\n    \"category\": \"Gėlių parduotuvė\",\n    \"totalScore\": 4.3,\n    \"rating\": 4.3,\n    \"reviewsCount\": 28,\n    \"location\": \"Gedimino pr. 15, Vilnius, Lithuania\",\n    \"name\": \"Gėlių Namai\"\n  },\n  {\n    \"title\": \"<PERSON><PERSON><PERSON>\",\n    \"address\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/kavine-sauletekis-kauna<PERSON>\",\n    \"categoryName\": \"Kavinė\",\n    \"category\": \"Kavinė\",\n    \"totalScore\": 4.7,\n    \"rating\": 4.7,\n    \"reviewsCount\": 45,\n    \"location\": \"Savanorių pr. 23, Kaunas, Lithuania\",\n    \"name\": \"Kavinė Saulėtekis\"\n  },\n  {\n    \"title\": \"Grožio Salonas Žvaigždė\",\n    \"address\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"phone\": \"+37060333333\",\n    \"phoneNumber\": \"+37060333333\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/grozio-salonas-zvaigzde\",\n    \"categoryName\": \"Grožio salonas\",\n    \"category\": \"Grožio salonas\",\n    \"totalScore\": 4.1,\n    \"rating\": 4.1,\n    \"reviewsCount\": 19,\n    \"location\": \"Manto g. 8, Klaipėda, Lithuania\",\n    \"name\": \"Grožio Salonas Žvaigždė\"\n  },\n  {\n    \"title\": \"Restoranas Šiaurės Žvaigždė\",\n    \"address\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"phone\": \"+37060444444\",\n    \"phoneNumber\": \"+37060444444\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/restoranas-siaures-zvaigzde\",\n    \"categoryName\": \"Restoranas\",\n    \"category\": \"Restoranas\",\n    \"totalScore\": 4.5,\n    \"rating\": 4.5,\n    \"reviewsCount\": 67,\n    \"location\": \"Tilžės g. 109, Šiauliai, Lithuania\",\n    \"name\": \"Restoranas Šiaurės Žvaigždė\"\n  },\n  {\n    \"title\": \"Autoservisas Greitas Ratas\",\n    \"address\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"phone\": \"+***********\",\n    \"phoneNumber\": \"+***********\",\n    \"website\": \"\", // No website - will be filtered\n    \"url\": \"https://maps.google.com/place/autoservisas-greitas-ratas\",\n    \"categoryName\": \"Autoservisas\",\n    \"category\": \"Autoservisas\",\n    \"totalScore\": 3.9,\n    \"rating\": 3.9,\n    \"reviewsCount\": 34,\n    \"location\": \"Pramonės g. 45, Vilnius, Lithuania\",\n    \"name\": \"Autoservisas Greitas Ratas\"\n  }\n];\n\nconsole.log('Mock Business Data - Generated test data:', mockBusinessData.length, 'businesses');\nreturn mockBusinessData.map(data => ({ json: data }));"}, "id": "2641b56b-8ac4-4d61-be3f-94452bdcdaae", "name": "Test Business Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4448, 2528]}, {"parameters": {"jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\nconst seenBusinesses = new Set(); // Track duplicates within this batch\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const businessName = data.title || data.name || 'Unknown';\n    const address = data.address || data.location || 'Unknown';\n    \n    // Create a unique key for deduplication (business name + address)\n    const uniqueKey = `${businessName.toLowerCase().trim()}_${address.toLowerCase().trim()}`;\n    \n    // Skip if we've already seen this business in this batch\n    if (seenBusinesses.has(uniqueKey)) {\n      console.log(`Skipping duplicate business: ${businessName}`);\n      continue;\n    }\n    \n    seenBusinesses.add(uniqueKey);\n    \n    const lead = {\n      business_name: businessName,\n      address: address,\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || '',\n      director_name: '', // Will be filled from rekvizitai\n      owner_email: '' // Will be filled from rekvizitai\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} unique businesses without websites (removed ${businesses.length - filteredLeads.length} duplicates)`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}, "id": "823e96c6-723e-4e28-9ec6-1facb9d82f0d", "name": "Filter Businesses Without Website", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4176, 2512]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-3600, 2560], "id": "4c422aaa-01df-46ad-b7be-d3b43550781c", "name": "Remove Duplicates"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw", "mode": "list", "cachedResultName": "Leads_Unfiltered", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone.replace('+', '') }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_status }}", "sms_status": "={{ $json.sms_status }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "626b0e47-c770-4a20-a9c9-72eacdef9ea0", "name": "Unfiltered_Leads", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3744, 2544], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}", "sms_status": "={{ $json.sms_status }}", "sms_sent": "={{ $json.sms_sent }}", "phone_source": "={{ $json.phone_source }}", "owner_phone": "={{ $json.owner_phone }}", "website": "={{ $json.website }}", "google_phone": "={{ $json.google_phone}}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "554d1cad-b9a2-460f-bd8f-8809424dcd68", "name": "Filtered_Leads", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3376, 2640], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  // Normalize reviews_count\n  let count = item.json.reviews_count;\n  if (count === undefined || count === null || count === '') {\n    item.json.reviews_count = 0;\n  } else {\n    item.json.reviews_count = Number(count);\n  }\n\n  // Normalize rating\n  let rating = item.json.rating;\n  if (rating === undefined || rating === null || rating === '') {\n    item.json.rating = 0;\n  } else {\n    item.json.rating = Number(rating);\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3936, 2544], "id": "b23c2b0e-6cfd-4d27-a262-088f825e4eb8", "name": "Normalise reviews, and"}, {"parameters": {"url": "https://app.sms8.io/services/send.php", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "dadea4239d37bc693307d751a7be0be596d80bed"}, {"name": "number", "value": "={{ ($json.google_phone || $json.owner_phone).replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"name": "message", "value": "=<PERSON><PERSON><PERSON>, <PERSON><PERSON> iš Upzera👋!\n\n<PERSON><PERSON> jauna komanda, pad<PERSON><PERSON> verslams sukurti svetaines ir pritraukti daugiau klientų pagerinant matomumą internetinėje erdvėje.\n\nNorėčiau trumpai pasidalinti idėja, kaip ne<PERSON>ti potencialių klientų – ar domintų sužinoti daugiau?\n\nupzera.com/lt"}, {"name": "device", "value": "[\"3771 | 0\"]"}, {"name": "type", "value": "sms"}, {"name": "prioritize", "value": "0"}]}, "options": {"response": {"response": {"responseFormat": "text", "outputPropertyName": "sms_response"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2160, 2640], "id": "95c83d7b-656f-4e8c-a03d-0b3bb6aaa62c", "name": "SMS"}], "pinData": {"When clicking ‘Execute workflow’": [{"json": {"query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Lietuva"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Test Business Data", "type": "main", "index": 0}]]}, "Prepare Leads": {"main": [[{"node": "Check Database for Existing Phones", "type": "main", "index": 0}]]}, "Check Database for Existing Phones": {"main": [[{"node": "<PERSON> SMS Status in Data", "type": "main", "index": 0}]]}, "Mark SMS Status in Data": {"main": [[{"node": "Update Google Sheet Status", "type": "main", "index": 0}]]}, "Update Google Sheet Status": {"main": [[{"node": "Filter Leads for SMS", "type": "main", "index": 0}]]}, "Filter Leads for SMS": {"main": [[{"node": "SMS", "type": "main", "index": 0}]]}, "Process SMS Response": {"main": [[{"node": "Update Final SMS Status", "type": "main", "index": 0}, {"node": "Record Successful SMS to Database", "type": "main", "index": 0}]]}, "Test Business Data": {"main": [[{"node": "Filter Businesses Without Website", "type": "main", "index": 0}]]}, "Filter Businesses Without Website": {"main": [[{"node": "Normalise reviews, and", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Filtered_Leads", "type": "main", "index": 0}]]}, "Unfiltered_Leads": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Normalise reviews, and": {"main": [[{"node": "Unfiltered_Leads", "type": "main", "index": 0}]]}, "Filtered_Leads": {"main": [[{"node": "Prepare Leads", "type": "main", "index": 0}]]}, "SMS": {"main": [[{"node": "Process SMS Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0ba56d7d-68f7-48e0-b2b5-3e4179db2d34", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e792ababaacd3609090c67925029f9ce56f8b2294355abafc6b7c9d2271c36bb"}, "id": "5JmQD48moWbfAuMi", "tags": []}